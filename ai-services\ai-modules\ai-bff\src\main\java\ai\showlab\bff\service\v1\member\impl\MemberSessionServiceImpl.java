package ai.showlab.bff.service.v1.member.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.common.security.token.JwtTokenProvider;
import ai.showlab.bff.entity.domain.v1.member.MemberSession;
import ai.showlab.bff.mapper.v1.member.MemberSessionMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.member.IMemberSessionService;
import ai.showlab.common.core.constant.CacheConstants;
import ai.showlab.common.core.utils.CommUtil;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.utils.ip.IpUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 会员会话服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Slf4j
@Service
public class MemberSessionServiceImpl extends BaseService implements IMemberSessionService {

    @Autowired
    private MemberSessionMapper sessionMapper;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Override
    @Transactional
    public MemberSession createSessionForMember(Long memberId, HttpServletRequest request) {
        // 1. 生成JWT Token
        Map<String, Object> claims = new HashMap<>();
        claims.put("memberId", memberId);
        // 可以添加更多用户信息到claims中
        String jwtToken = jwtTokenProvider.generateToken(memberId.toString(), claims);

        // 2. 使用JWT Token创建会话
        return createSessionWithJwtToken(memberId, jwtToken, request);
    }

    @Override
    @Transactional
    public MemberSession createSessionWithJwtToken(Long memberId, String jwtToken, HttpServletRequest request) {
        // 1. 从JWT Token获取过期时间
        Date jwtExpiration = jwtTokenProvider.getExpirationDateFromToken(jwtToken);

        // 2. 准备会话信息
        MemberSession session = new MemberSession();
        session.setMemberId(memberId);
        // 直接存储JWT Token
        session.setToken(jwtToken);
        // TODO: 根据 request.getHeader("User-Agent") 转换类型
        session.setDeviceType(1);
        // TODO: 可通过更复杂的库解析 User-Agent 获得
        session.setDeviceInfo("Unknown");
        session.setIpAddress(IpUtils.getIpAddr(request));
        session.setLoginTime(CommUtil.toOffsetDateTime(null));
        // 使用JWT的过期时间，确保一致性
        session.setExpireTime(CommUtil.toOffsetDateTime(jwtExpiration));
        session.setIsActive(true);
        log.info("----session: {}", JSON.toJSONString(session));
        // 3. 插入数据库
        sessionMapper.insertMemberSession(session);
        log.info("为用户 {} 创建会话成功，JWT过期时间: {}", memberId, jwtExpiration);
        return session;
    }

    @Override
    public MemberSession validateSession(String token) {
        // 1. 构建缓存键
        String cacheKey = CacheConstants.BFF_MEMBER_SESSION_BY_TOKEN_KEY + "::" + token;
        String subToken = token.substring(0, Math.min(10, token.length()));

        // 2. 尝试从缓存获取
        MemberSession cachedSession = (MemberSession) redisTemplate.opsForValue().get(cacheKey);
        if (cachedSession != null) {
            log.debug("Session缓存命中，token: {}", subToken);
            return cachedSession;
        }

        // 3. 缓存未命中，首先验证JWT Token的有效性（签名、过期等）
        try {
            if (!jwtTokenProvider.validateToken(token)) {
                log.debug("JWT Token验证失败: {}", subToken);
                return null;
            }
        } catch (Exception e) {
            log.debug("JWT Token验证异常: {}, token: {}", e.getMessage(), subToken);
            return null;
        }

        // 4. JWT验证通过后，查询Session记录（用于设备管理和审计）
        // XML中已包含对 is_active, expire_time, delete_time 的判断
        MemberSession session = sessionMapper.selectMemberSessionByToken(token);
        if (session != null) {
            log.debug("Session验证成功，用户ID: {}, 设备: {}", session.getMemberId(), session.getDeviceInfo());

            // 5. 将结果缓存，使用与JWT相同的过期时间
            long ttlSeconds = jwtTokenProvider.getExpirationInSeconds();
            redisTemplate.opsForValue().set(cacheKey, session, ttlSeconds, TimeUnit.SECONDS);
            log.debug("Session已缓存，TTL: {} 秒，token: {}", ttlSeconds, subToken);
        } else {
            log.debug("Session记录不存在或已失效，token: {}", subToken);
        }
        return session;
    }

    @Override
    @CustomCache(value = CacheConstants.BFF_MEMBER_SESSIONS_BY_ID_KEY, key = "#memberId", ttl = 30)
    public List<MemberSession> getActiveSessionsForMember(Long memberId) {
        // XML查询中已经包含了 is_active = true 和 delete_time is null 的条件
        List<MemberSession> sessions = sessionMapper.selectMemberSessionsByMemberId(memberId);
        log.debug("查询到用户 {} 的活跃会话数量: {}", memberId, sessions.size());
        // 调试信息：打印查询结果
        if (log.isDebugEnabled()) {
            for (MemberSession session : sessions) {
                log.debug("数据库查询到会话 - ID: {}, IP: {}, 活跃状态: {}, 登录时间: {}",
                        session.getId(), session.getIpAddress(), session.getIsActive(), session.getLoginTime());
            }
        }
        return sessions;
    }

    @Override
    @Transactional
    public void terminateSession(Long memberId, Long sessionId) {
        MemberSession session = sessionMapper.selectMemberSessionById(sessionId);
        if (session == null || !session.getMemberId().equals(memberId)) {
            log.warn("尝试终止不存在或不属于用户 {} 的会话，会话ID: {}", memberId, sessionId);
            return; // 静默处理
        }

        log.info("开始终止用户 {} 的会话，会话ID: {}, Token前缀: {}",
                memberId, sessionId, session.getToken() != null ? session.getToken().substring(0, Math.min(8, session.getToken().length())) : "null");

        // 先让数据库记录失效
        int updatedRows = sessionMapper.deactivateMemberSession(session.getToken());
        log.info("数据库更新结果：用户 {} 的会话 {} 更新了 {} 行记录", memberId, sessionId, updatedRows);

        // 手动清除 token 对应的缓存
        String cacheKey = CacheConstants.BFF_MEMBER_SESSION_BY_TOKEN_KEY + "::" + session.getToken();
        redisTemplate.delete(cacheKey);
        log.debug("已清除Token缓存: {}", session.getToken().substring(0, Math.min(8, session.getToken().length())));

        // 终止会话后刷新缓存
        refreshSessionCache(memberId);
        log.info("成功终止用户 {} 的会话 {}", memberId, sessionId);
    }

    @Override
    @Transactional
    public void terminateSessionByToken(Long memberId, String jwtToken) {
        // 1. 首先验证JWT Token的有效性
        try {
            if (!jwtTokenProvider.validateToken(jwtToken)) {
                log.warn("尝试终止无效的JWT Token会话，用户ID: {}", memberId);
                return;
            }
        } catch (Exception e) {
            log.warn("JWT Token验证失败，无法终止会话，用户ID: {}, 错误: {}", memberId, e.getMessage());
            return;
        }

        // 2. 查找对应的会话记录
        MemberSession session = sessionMapper.selectMemberSessionByToken(jwtToken);
        if (session == null) {
            log.warn("未找到对应的会话记录，用户ID: {}, Token前缀: {}",
                    memberId, jwtToken.substring(0, Math.min(10, jwtToken.length())));
            return;
        }

        if (!session.getMemberId().equals(memberId)) {
            log.warn("会话所属用户不匹配，期望: {}, 实际: {}", memberId, session.getMemberId());
            return;
        }

        // 3. 终止会话
        terminateSession(memberId, session.getId());
        log.info("根据JWT Token成功终止用户 {} 的会话", memberId);
    }

    @Override
    @Transactional
    public void terminateAllSessions(Long memberId) {
        // 此操作会影响所有该用户的 token 缓存，但无法批量清除，
        // 缓存会在过期后自动失效，这是一个可接受的折中。
        sessionMapper.deactivateAllMemberSessions(memberId);

        // 终止所有会话后刷新缓存（移除@CacheEvict，改为主动刷新）
        refreshSessionCache(memberId);
    }

    @Override
    public void refreshSessionCache(Long memberId) {
        try {
            // 1. 从数据库查询最新的活跃会话
            List<MemberSession> latestSessions = sessionMapper.selectMemberSessionsByMemberId(memberId);
            log.debug("从数据库查询到用户 {} 的最新活跃会话数量: {}", memberId, latestSessions.size());

            // 2. 手动更新缓存
            Cache sessionListCache = cacheManager.getCache(CacheConstants.BFF_MEMBER_SESSIONS_BY_ID_KEY);
            if (sessionListCache != null) {
                String cacheKey = memberId.toString();
                sessionListCache.put(cacheKey, latestSessions);
                log.debug("成功更新用户 {} 的会话列表缓存，会话数量: {}", memberId, latestSessions.size());
            } else {
                log.warn("无法获取会话列表缓存管理器");
            }
        } catch (Exception e) {
            log.error("刷新用户 {} 的会话列表缓存失败: {}", memberId, e.getMessage(), e);
        }
    }
}