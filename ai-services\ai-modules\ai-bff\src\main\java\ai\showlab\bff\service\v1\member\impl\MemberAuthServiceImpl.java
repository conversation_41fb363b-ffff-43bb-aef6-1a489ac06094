package ai.showlab.bff.service.v1.member.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.common.security.token.JwtTokenProvider;
import ai.showlab.bff.common.util.BffKit;
import ai.showlab.bff.entity.domain.v1.member.Member;
import ai.showlab.bff.entity.domain.v1.member.MemberAuth;
import ai.showlab.bff.entity.param.*;
import ai.showlab.bff.mapper.v1.member.MemberAuthMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.common.INotificationService;
import ai.showlab.bff.service.v1.func.IFuncRoleService;
import ai.showlab.bff.service.v1.member.*;
import ai.showlab.common.core.constant.CacheConstants;
import ai.showlab.common.core.constant.CodeConstants;
import ai.showlab.common.protocol.enums.AuthTypeEnum;
import ai.showlab.common.protocol.enums.MemberLevelEnum;
import ai.showlab.common.protocol.enums.MemberStatusEnum;
import ai.showlab.common.protocol.enums.MemberTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 会员认证服务实现类
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Service
public class MemberAuthServiceImpl extends BaseService implements IMemberAuthService {

    private static final Logger log = LoggerFactory.getLogger(MemberAuthServiceImpl.class);

    @Autowired
    private MemberAuthMapper memberAuthMapper;

    // 假设密码加密器已在Spring容器中配置
    @Autowired
    private BCryptPasswordEncoder passwordEncoder;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private JwtTokenProvider jwtTokenProvider;
    @Autowired
    private INotificationService notificationService;

    @Autowired
    private IFuncRoleService funcRoleService;

    @Autowired
    private IMemberRoleService memberRoleService;

    @Autowired
    private IMemberSessionService memberSessionService;

    @Autowired
    private IDuplicateLoginService duplicateLoginService;

    @Autowired
    private CacheManager cacheManager;

    @Override
    @Transactional
    public void registerNewAuth(Long memberId, Integer authType, String identifier, String credential) {
        // 参数验证
        if (memberId == null) {
            throw new BusinessException("会员ID不能为空");
        }
        if (authType == null) {
            throw new BusinessException("认证类型不能为空");
        }
        if (!StringUtils.hasText(identifier)) {
            throw new BusinessException("认证标识不能为空");
        }
        if (!StringUtils.hasText(credential)) {
            throw new BusinessException("认证凭证不能为空");
        }

        // 检查该认证方式是否已存在
        if (memberAuthMapper.selectMemberAuthByIdentifier(authType, identifier) != null) {
            throw new BusinessException("该" + AuthTypeEnum.getByCode(authType).getInfo() + "已被注册");
        }

        MemberAuth newAuth = new MemberAuth();
        newAuth.setMemberId(memberId);
        newAuth.setAuthType(authType);
        newAuth.setIdentifier(identifier);
        // 存储加密后的凭证
        newAuth.setCredential(passwordEncoder.encode(credential));
        // TODO: 默认需要验证，例如邮箱验证
        newAuth.setIsVerified(false);

        memberAuthMapper.insertMemberAuth(newAuth);
        log.info("为会员 {} 注册新的认证方式成功，类型: {}, 标识: {}", memberId, AuthTypeEnum.getByCode(authType).getInfo(), identifier);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void register(RequestParams<MemberRegisterParam> requestParams) {
        MemberRegisterParam param = requestParams.getBizParam();
        String username = param.getUsername();
        String password = param.getPassword();
        String email = param.getEmail();
        String phone = param.getPhone();
        // 1. 唯一性检查
        if (StringUtils.hasText(username) && memberService.isUsernameExists(username)) {
            throw new BusinessException(CodeConstants.USER_ALREADY_EXISTS, "用户名已被注册");
        }
        if (StringUtils.hasText(email) && memberService.isEmailExists(email)) {
            throw new BusinessException("邮箱已被注册");
        }
        if (StringUtils.hasText(phone) && memberService.isPhoneExists(phone)) {
            throw new BusinessException("手机号已被注册");
        }

        // 2. 创建新会员
        // 校验密码复杂度
        if (isInvalidPassword(password)) {
            throw new BusinessException("密码不符合复杂度要求。密码必须包含大小写字母、数字和特殊字符，且长度至少为8位");
        }
        Member newMember = new Member();
        newMember.setUsername(username);
        newMember.setNickname(createNickName(param));
        newMember.setEmail(email);
        newMember.setPhone(phone);
        newMember.setPassword(passwordEncoder.encode(password));
        newMember.setMemberType(MemberTypeEnum.NORMAL.getCode());
        newMember.setMemberLevel(MemberLevelEnum.FREE.getCode());
        newMember.setStatus(MemberStatusEnum.NORMAL.getCode());

        Long newMemberId = memberService.createMember(newMember);

        // 3. 注册认证方式
        // 至少需要一种认证方式
        boolean hasAuthMethod = false;

        // 如果提供了用户名，则注册密码认证
        if (StringUtils.hasText(username)) {
            registerNewAuth(newMemberId, AuthTypeEnum.PASSWORD.getCode(), username, password);
            hasAuthMethod = true;
        }

        // 如果提供了邮箱，则注册邮箱认证
        if (StringUtils.hasText(email)) {
            registerNewAuth(newMemberId, AuthTypeEnum.EMAIL.getCode(), email, password);
            hasAuthMethod = true;
        }

        // 如果提供了手机号，则注册手机号认证
        if (StringUtils.hasText(phone)) {
            registerNewAuth(newMemberId, AuthTypeEnum.PHONE.getCode(), phone, password);
            hasAuthMethod = true;
        }

        // 确保至少有一种认证方式
        if (!hasAuthMethod) {
            throw new BusinessException("注册失败：至少需要提供用户名、邮箱或手机号中的一种作为登录方式");
        }

        // 4. 为新用户分配默认角色
        assignDefaultRoleToNewMember(newMemberId);
    }

    /**
     * 为新注册的会员分配默认角色
     *
     * @param memberId 会员ID
     */
    private void assignDefaultRoleToNewMember(Long memberId) {
        try {
            Long defaultRoleId = funcRoleService.getDefaultRoleId();
            if (defaultRoleId != null) {
                memberRoleService.grantRolesToMember(memberId, List.of(defaultRoleId));
                log.info("为新用户 {} 分配默认角色成功，角色ID: {}", memberId, defaultRoleId);
            } else {
                log.error("无法为新用户 {} 分配默认角色，默认角色(code='normal')不存在，请检查数据库配置", memberId);
                // 这里可以选择抛出异常或者继续执行，根据业务需求决定
                // throw new BusinessException("系统配置错误：默认角色不存在");
            }
        } catch (Exception e) {
            log.error("为新用户 {} 分配默认角色失败: {}", memberId, e.getMessage(), e);
            // 角色分配失败不应该影响用户注册，所以这里只记录日志
            // 可以考虑发送告警通知运维人员
        }
    }

    /**
     * 生成默认呢称
     *
     * @param param 请求参数
     * @return 呢称
     */
    private String createNickName(MemberRegisterParam param) {
        if (StringUtils.hasText(param.getEmail())) {
            // 使用邮箱前缀作为默认呢称
            return param.getEmail().split("@")[0];
        }
        if (StringUtils.hasText(param.getPhone())) {
            // 使用手机号作为默认呢称
            return "m" + param.getPhone().substring(5, 11);
        }
        // 生成随机呢称
        return "m" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String login(RequestParams<MemberLoginParam> requestParams) {
        // 获取业务参数
        MemberLoginParam param = requestParams.getBizParam();
        String identifier = param.getIdentifier();
        String password = param.getPassword();

        // 1. 判断标识类型并验证凭证
        Integer authType;
        if (identifier.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
            authType = AuthTypeEnum.EMAIL.getCode();
        } else if (identifier.matches("^\\d{11}$")) {
            authType = AuthTypeEnum.PHONE.getCode();
        } else {
            // 假设是用户名
            authType = AuthTypeEnum.PASSWORD.getCode();
        }

        MemberAuth auth = verifyCredential(authType, identifier, password);

        // 2. 获取用户信息，用于JWT Claims
        Member member = memberService.getMemberProfileById(auth.getMemberId());
        if (member == null) {
            throw new BusinessException("用户信息不存在");
        }

        // 3. 检查重复登录
        if (requestParams.getRequest() != null) {
            IDuplicateLoginService.DuplicateLoginResult duplicateLoginResult =
                    duplicateLoginService.checkAndHandleDuplicateLogin(auth.getMemberId(), requestParams.getRequest());

            if (!duplicateLoginResult.isAllowed()) {
                throw new BusinessException("登录被拒绝：" + duplicateLoginResult.getMessage());
            }

            // 记录重复登录处理结果，并刷新缓存
            if (duplicateLoginResult.getKickedSessions() != null && !duplicateLoginResult.getKickedSessions().isEmpty()) {
                log.info("用户 {} 登录时踢掉了 {} 个旧会话",
                        auth.getMemberId(), duplicateLoginResult.getKickedSessions().size());

                // 踢掉旧会话后立即刷新缓存
                memberSessionService.refreshSessionCache(auth.getMemberId());
                log.debug("踢掉旧会话后为用户 {} 刷新会话列表缓存", auth.getMemberId());
            }
        }

        // 4. 生成JWT Token，包含完整的会员信息
        Map<String, Object> claims = new HashMap<>();
        claims.put("memberId", auth.getMemberId());
        claims.put("username", getUsername(auth, identifier, authType));
        claims.put("nickname", member.getNickname());
        claims.put("authType", authType);
        String token = jwtTokenProvider.generateToken(String.valueOf(auth.getMemberId()), claims);

        // 5. 创建会话记录（Session管理）- 使用JWT Token统一管理
        try {
            if (requestParams.getRequest() != null) {
                memberSessionService.createSessionWithJwtToken(auth.getMemberId(), token, requestParams.getRequest());
                log.info("为用户 {} 创建会话记录成功，使用JWT Token统一管理", auth.getMemberId());

                // 主动更新会话列表缓存
                memberSessionService.refreshSessionCache(auth.getMemberId());
                log.info("为用户 {} 刷新会话列表缓存成功", auth.getMemberId());
            }
        } catch (Exception e) {
            log.error("创建会话记录失败，用户ID: {}, 错误: {}", auth.getMemberId(), e.getMessage(), e);
            // 会话记录创建失败不影响登录，继续执行
        }

        // 6. 更新会员最后登录信息
        String clientIp = requestParams.getClientIp();
        memberService.updateMemberLastLoginInfo(auth.getMemberId(), clientIp, OffsetDateTime.now());

        log.info("会员登录成功，会员ID: {}, 标识: {}, 认证类型: {}, IP: {}",
                auth.getMemberId(), identifier, AuthTypeEnum.getByCode(authType).getInfo(), clientIp);
        return token;
    }

    /**
     * 获取用户名，用于JWT Claims
     *
     * @param auth       认证信息
     * @param identifier 登录标识
     * @param authType   认证类型
     * @return 用户名
     */
    private String getUsername(MemberAuth auth, String identifier, Integer authType) {
        // 根据认证类型返回合适的用户名
        if (AuthTypeEnum.PASSWORD.getCode().equals(authType)) {
            // 用户名登录，直接返回用户名
            return identifier;
        } else {
            // 邮箱或手机号登录，尝试获取用户名认证记录
            List<MemberAuth> auths = memberAuthMapper.selectMemberAuthByMemberId(auth.getMemberId());
            return auths.stream()
                    .filter(a -> AuthTypeEnum.PASSWORD.getCode().equals(a.getAuthType()))
                    .map(MemberAuth::getIdentifier)
                    .findFirst()
                    // 如果没有用户名，使用登录标识
                    .orElse(identifier);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void logout(Long memberId, String token) {
        try {
            // 1. 将JWT Token加入黑名单
            Date expirationDate = jwtTokenProvider.getExpirationDateFromToken(token);
            // 计算剩余有效时间 (毫秒)
            long ttl = expirationDate.getTime() - System.currentTimeMillis();
            if (ttl > 0) {
                redisTemplate.opsForValue().set(CacheConstants.BFF_TOKEN_BLACKLIST_KEY + ":" + token, "blacklisted", ttl, TimeUnit.MILLISECONDS);
                log.info("JWT Token已加入黑名单，用户ID: {}, TTL: {}ms", memberId, ttl);
            } else {
                log.warn("尝试将已过期或即将过期的Token加入黑名单，用户ID: {}", memberId);
            }

            // 2. 终止当前会话记录（Session管理）- 基于JWT Token精确匹配
            try {
                memberSessionService.terminateSessionByToken(memberId, token);
                log.info("已终止用户 {} 的当前会话记录", memberId);

                // 终止会话后刷新缓存
                memberSessionService.refreshSessionCache(memberId);
                log.debug("登出后为用户 {} 刷新会话列表缓存", memberId);
            } catch (Exception e) {
                log.error("终止会话记录失败，用户ID: {}, 错误: {}", memberId, e.getMessage(), e);
                // 会话记录终止失败不影响登出，继续执行
            }

            // 3. 清除会员相关的所有缓存
            try {
                clearMemberCaches(memberId);
                log.info("已清除会员 {} 的所有缓存", memberId);
            } catch (Exception e) {
                log.error("清除会员缓存失败，会员ID: {}, 错误: {}", memberId, e.getMessage(), e);
                // 缓存清除失败不影响登出，继续执行
            }

            log.info("用户登出成功，用户ID: {}", memberId);
        } catch (Exception e) {
            log.error("用户登出处理失败，用户ID: {}, 错误: {}", memberId, e.getMessage(), e);
            throw new BusinessException("登出失败，请稍后重试");
        }
    }

    /**
     * 清除会员相关的所有缓存
     *
     * @param memberId 会员ID
     */
    private void clearMemberCaches(Long memberId) {
        try {
            // 清除会员信息缓存
            Cache profileCache = cacheManager.getCache(CacheConstants.BFF_MEMBER_PROFILE);
            if (profileCache != null) {
                profileCache.evict(memberId);
                log.debug("已清除用户 {} 的profile缓存", memberId);
            }

            Cache memberInfoCache = cacheManager.getCache(CacheConstants.BFF_MEMBER_INFO_VO);
            if (memberInfoCache != null) {
                memberInfoCache.evict(memberId);
                log.debug("已清除用户 {} 的基本信息缓存", memberId);
            }

            // 清除用户认证信息缓存
            Cache authCache = cacheManager.getCache(CacheConstants.BFF_MEMBER_AUTH_KEY);
            if (authCache != null) {
                authCache.evict(memberId);
                log.debug("已清除用户 {} 的auth缓存", memberId);
            }

            // 清除会话列表缓存（使用新的缓存键格式）
            String sessionCacheKey = CacheConstants.BFF_MEMBER_SESSIONS_BY_ID_KEY + "::" + memberId;
            redisTemplate.delete(sessionCacheKey);
            log.debug("已清除用户 {} 的sessions缓存", memberId);

            // 清除其他可能的用户相关缓存
            // 如果将来有更多用户相关的缓存，可以在这里添加

        } catch (Exception e) {
            log.error("清除用户 {} 的缓存时发生异常: {}", memberId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @CustomCache(value = CacheConstants.BFF_MEMBER_AUTH_KEY, key = "#memberId")
    public List<MemberAuth> getMemberAuths(Long memberId) {
        return memberAuthMapper.selectMemberAuthByMemberId(memberId);
    }

    @Override
    public MemberAuth verifyCredential(Integer authType, String identifier, String rawCredential) {
        MemberAuth auth = memberAuthMapper.selectAuthForVerification(authType, identifier);
        if (auth == null) {
            throw new BusinessException("认证信息不存在");
        }

        if (!passwordEncoder.matches(rawCredential, auth.getCredential())) {
            // 此处可以增加登录失败次数记录等安全策略
            throw new BusinessException("凭证错误");
        }

        return auth;
    }

    @Override
    @Transactional
    @CacheEvict(value = CacheConstants.BFF_MEMBER_AUTH_KEY, key = "#memberId")
    public void changeCredential(Long memberId, Integer authType, String oldCredential, String newCredential) {
        // 1. 如果提供了旧凭证，则先进行验证
        if (StringUtils.hasText(oldCredential)) {
            MemberAuth auth = memberAuthMapper.selectMemberAuthByMemberId(memberId)
                    .stream()
                    // 过滤掉软删除的
                    .filter(a -> a.getAuthType().equals(authType) && a.getDeleteTime() == null)
                    .findFirst()
                    .orElseThrow(() -> new BusinessException("认证方式不存在"));

            verifyCredential(authType, auth.getIdentifier(), oldCredential);
        }

        // 2. 更新为新的加密凭证
        String newEncodedCredential = passwordEncoder.encode(newCredential);
        int result = memberAuthMapper.updateMemberAuthCredential(memberId, authType, newEncodedCredential);
        if (result == 0) {
            throw new BusinessException("凭证更新失败");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void changePassword(RequestParams<ChangePasswordParam> requestParams) {
        Long currentMemberId = BffKit.getCurrentMemberId();
        // 获取业务参数
        ChangePasswordParam param = requestParams.getBizParam();
        // 直接调用 changeCredential 方法，指定密码认证类型
        changeCredential(currentMemberId, AuthTypeEnum.PASSWORD.getCode(), param.getOldPassword(), param.getNewPassword());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void sendPasswordResetCode(RequestParams<SendPasswordResetCodeParam> requestParams) {
        // 获取业务参数
        SendPasswordResetCodeParam param = requestParams.getBizParam();
        String identifier = param.getIdentifier();

        // 1. 检查用户标识是否存在并判断类型
        // 更通用的邮箱判断
        boolean isEmail = identifier.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
        // 11位数字
        boolean isPhone = identifier.matches("^\\d{11}$");

        boolean memberExists;
        if (isEmail) {
            memberExists = memberService.isEmailExists(identifier);
            if (!memberExists) {
                throw new BusinessException("邮箱不存在");
            }
        } else if (isPhone) {
            memberExists = memberService.isPhoneExists(identifier);
            if (!memberExists) {
                throw new BusinessException("手机号不存在");
            }
        } else { // 假设是用户名
            memberExists = memberService.isUsernameExists(identifier);
            if (!memberExists) {
                throw new BusinessException("用户名不存在");
            }
            // 对于用户名，如果存在，需要获取其绑定的邮箱或手机号来发送验证码
            // 这里简化为，如果通过用户名找到了用户，但没有绑定邮箱或手机，则报错
            // TODO: 在真实场景中，这里需要从 memberService 获取用户的邮箱或手机号
            throw new BusinessException("无法通过用户名发送验证码，请确保您已绑定邮箱或手机号");
        }

        // 2. 生成验证码
        // TODO: 模拟验证码，实际应生成随机数
        String code = "123456";

        // 将验证码存储到 Redis 中，并设置过期时间 (5分钟)
        String key = CacheConstants.PASSWORD_RESET_CODE_PREFIX + identifier;
        redisTemplate.opsForValue().set(key, code, 5, TimeUnit.MINUTES);
        System.out.println("发送密码重置验证码: " + code + " 到 " + identifier + ", 已存入Redis, key: " + key);

        // 调用通知服务发送验证码
        if (isEmail) {
            notificationService.sendEmailCode(identifier, code);
        } else if (isPhone) {
            notificationService.sendSmsCode(identifier, code);
        } else {
            // 如果是用户名，且已经通过 isUsernameExists 检查，则需要获取其绑定信息
            // 由于目前没有获取绑定信息的方法，这里视为无法发送，或者可以根据具体业务逻辑决定
            // 为了让测试通过，这里可以考虑不抛出异常，或者抛出更明确的异常
            log.warn("sendPasswordResetCode: 无法通过用户名 {} 发送验证码，请确保用户已绑定邮箱或手机号，或实现相关逻辑", identifier);
            throw new BusinessException("无法发送验证码，请确保您已绑定邮箱或手机号");
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void resetPassword(RequestParams<ResetPasswordParam> requestParams) {
        // 获取业务参数
        ResetPasswordParam param = requestParams.getBizParam();
        String identifier = param.getIdentifier();
        String code = param.getCode();
        String newPassword = param.getNewPassword();
        // 1. 校验验证码
        String key = CacheConstants.PASSWORD_RESET_CODE_PREFIX + identifier;
        String storedCode = (String) redisTemplate.opsForValue().get(key);

        if (!StringUtils.hasText(storedCode) || !code.equals(storedCode)) {
            throw new BusinessException("验证码无效或已过期");
        }
        // 2. 找到对应的认证信息并更新密码
        // 校验密码复杂度
        if (isInvalidPassword(newPassword)) {
            throw new BusinessException("新密码不符合复杂度要求。密码必须包含大小写字母、数字和特殊字符，且长度至少为8位");
        }
        MemberAuth auth = memberAuthMapper.selectAuthForVerification(AuthTypeEnum.PASSWORD.getCode(), identifier);
        if (auth == null) {
            throw new BusinessException("用户认证信息不存在");
        }
        // 直接更新密码，不校验旧密码
        changeCredential(auth.getMemberId(), AuthTypeEnum.PASSWORD.getCode(), null, newPassword);
        // 重置密码成功后，删除Redis中的验证码
        redisTemplate.delete(key);
    }

    @Override
    @Transactional
    @CacheEvict(value = CacheConstants.BFF_MEMBER_AUTH_KEY, key = "#memberId")
    public void unbindAuth(Long memberId, Integer authType) {
        if (!AuthTypeEnum.existsByCode(authType)) {
            throw new BusinessException("无效的认证类型");
        }
        MemberAuth auth = memberAuthMapper.selectMemberAuthByMemberIdAndAuthType(memberId, authType);
        if (auth != null) {
            // 软删除
            memberAuthMapper.softDeleteMemberAuth(auth.getId());
        } else {
            throw new BusinessException("未找到该认证方式");
        }
    }

    private boolean isInvalidPassword(String password) {
        // 密码必须包含大小写字母、数字和特殊字符，且长度至少为8位
        String regex = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*()_+\\[\\]{};':\",.<>/?\\-|]).{8,}$";
        return !password.matches(regex);
    }
} 