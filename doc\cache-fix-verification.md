# 缓存类型转换问题修复验证

## 问题背景

在修改session缓存实现为手动管理后，出现了`ClassCastException`错误：
```
java.lang.ClassCastException: class com.alibaba.fastjson2.JSONObject cannot be cast to class ai.showlab.bff.entity.domain.v1.member.MemberSession
```

## 修复内容

### 1. 添加类型转换方法

在`MemberSessionServiceImpl`中添加了两个类型转换方法：

#### convertCachedSession方法
```java
private MemberSession convertCachedSession(Object cachedValue) {
    try {
        if (cachedValue == null) {
            return null;
        }

        // 如果已经是正确的类型，直接返回
        if (cachedValue instanceof MemberSession) {
            return (MemberSession) cachedValue;
        }

        // 如果是JSONObject，需要转换
        if (cachedValue instanceof com.alibaba.fastjson2.JSONObject) {
            return JSON.parseObject(JSON.toJSONString(cachedValue), MemberSession.class);
        }

        // 其他情况，尝试通过JSON转换
        return JSON.parseObject(JSON.toJSONString(cachedValue), MemberSession.class);

    } catch (Exception e) {
        log.warn("转换缓存的会话数据失败: {}, 将重新从数据库查询", e.getMessage());
        return null;
    }
}
```

#### convertCachedSessionList方法
```java
private List<MemberSession> convertCachedSessionList(Object cachedValue) {
    try {
        if (cachedValue == null) {
            return null;
        }

        // 如果已经是正确的类型，直接返回
        if (cachedValue instanceof List) {
            List<?> list = (List<?>) cachedValue;
            if (list.isEmpty()) {
                return (List<MemberSession>) list;
            }
            
            // 检查第一个元素的类型
            Object firstElement = list.get(0);
            if (firstElement instanceof MemberSession) {
                return (List<MemberSession>) list;
            }
            
            // 如果是JSONObject，需要转换
            if (firstElement instanceof com.alibaba.fastjson2.JSONObject) {
                return list.stream()
                        .map(item -> JSON.parseObject(JSON.toJSONString(item), MemberSession.class))
                        .toList();
            }
        }

        // 其他情况，尝试通过JSON转换
        String jsonString = JSON.toJSONString(cachedValue);
        return JSON.parseArray(jsonString, MemberSession.class);

    } catch (Exception e) {
        log.warn("转换缓存的会话列表数据失败: {}, 将重新从数据库查询", e.getMessage());
        return null;
    }
}
```

### 2. 修改缓存获取逻辑

#### validateSession方法
```java
// 修改前
MemberSession cachedSession = (MemberSession) redisTemplate.opsForValue().get(cacheKey);

// 修改后
Object cachedValue = redisTemplate.opsForValue().get(cacheKey);
MemberSession cachedSession = convertCachedSession(cachedValue);
```

#### getActiveSessionsForMember方法
```java
// 修改前
@SuppressWarnings("unchecked")
List<MemberSession> cachedSessions = (List<MemberSession>) redisTemplate.opsForValue().get(cacheKey);

// 修改后
Object cachedValue = redisTemplate.opsForValue().get(cacheKey);
List<MemberSession> cachedSessions = convertCachedSessionList(cachedValue);
```

## 验证步骤

### 1. 单元测试验证
运行`CacheTypeConversionTest`测试类：
```bash
cd ai-services/ai-modules/ai-bff
mvn test -Dtest=CacheTypeConversionTest
```

### 2. 集成测试验证
运行`MemberSessionServiceImplTest`测试类：
```bash
mvn test -Dtest=MemberSessionServiceImplTest
```

### 3. 功能测试验证

#### 清理环境
```bash
# 清理Redis缓存
redis-cli del "bff:member:sessions:id::16"
redis-cli keys "bff:member:session:token::*" | xargs redis-cli del
```

#### 测试登录功能
```bash
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "13800138000",
    "password": "aaBB@888"
  }'
```

#### 检查缓存
```bash
# 检查会话列表缓存
redis-cli get "bff:member:sessions:id::16"

# 检查缓存TTL
redis-cli ttl "bff:member:sessions:id::16"
```

### 4. 重复登录测试
```bash
# 第二次登录，触发重复登录处理
curl -X POST http://localhost:9901/ai-bff/api/v1/member/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "13800138000",
    "password": "aaBB@888"
  }'
```

## 预期结果

1. **不再出现ClassCastException错误**
2. **缓存TTL与JWT过期时间一致**（3600秒）
3. **重复登录功能正常工作**
4. **缓存命中率提升**
5. **数据库查询次数减少**

## 监控指标

### 1. 错误日志
监控应用日志，确保不再出现类型转换异常：
```bash
tail -f logs/ai-bff.log | grep -i "ClassCastException"
```

### 2. 缓存命中率
监控缓存相关的DEBUG日志：
```bash
tail -f logs/ai-bff.log | grep -i "缓存命中"
```

### 3. 数据库查询
监控数据库查询日志，验证查询次数减少：
```bash
tail -f logs/ai-bff.log | grep -i "查询到用户.*的活跃会话数量"
```

## 回滚方案

如果修复出现问题，可以快速回滚到注解方式：

1. 恢复`@CustomCache`注解
2. 移除手动缓存管理代码
3. 设置合适的TTL值

```java
// 回滚示例
@CustomCache(value = CacheConstants.BFF_MEMBER_SESSIONS_BY_ID_KEY, key = "#memberId", ttl = 3600)
public List<MemberSession> getActiveSessionsForMember(Long memberId) {
    return sessionMapper.selectMemberSessionsByMemberId(memberId);
}
```
