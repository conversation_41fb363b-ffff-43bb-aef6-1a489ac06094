# Session缓存时间与JWT过期时间一致性改进

## 问题分析

### 原始问题
发现了两个与JWT过期时间不一致的缓存配置：

1. **`validateSession`方法**: session缓存使用的是 `@CustomCache` 注解的默认TTL（3600秒）
2. **`getActiveSessionsForMember`方法**: 会话列表缓存TTL设置为30秒

而JWT的过期时间是通过配置文件 `jwt.expiration`（3600000毫秒 = 3600秒）设置的。

### 具体问题
1. **配置漂移风险**: 如果修改JWT过期时间但忘记同步session缓存时间
2. **维护困难**: 多个地方分别配置，容易出现不一致
3. **缓存失效时机不匹配**: 可能出现JWT有效但缓存过期，或JWT过期但缓存仍存在的情况
4. **会话列表缓存过短**: 30秒的TTL导致频繁的数据库查询，影响性能

### 配置位置
- **JWT过期时间**: `doc/nacos/ai-bff-dev.yml` 中的 `jwt.expiration: 3600000` (毫秒)
- **Session缓存时间**: `@CustomCache` 注解的 `ttl` 参数
  - `validateSession`: 默认3600秒
  - `getActiveSessionsForMember`: 30秒 ❌

## 解决方案

### 1. 添加JWT过期时间获取方法
在 `JwtTokenProvider` 中添加了 `getExpirationInSeconds()` 方法：

```java
/**
 * 获取JWT过期时间配置（以秒为单位）
 * 用于与缓存TTL保持一致
 *
 * @return JWT过期时间（秒）
 */
public long getExpirationInSeconds() {
    return expiration / 1000;
}
```

### 2. 重构validateSession和getActiveSessionsForMember方法
将原来的 `@CustomCache` 注解改为手动缓存管理，使用动态TTL：

#### validateSession方法

```java
@Override
public MemberSession validateSession(String token) {
    // 1. 构建缓存键
    String cacheKey = CacheConstants.BFF_MEMBER_SESSION_BY_TOKEN_KEY + "::" + token;
    
    // 2. 尝试从缓存获取
    MemberSession cachedSession = (MemberSession) redisTemplate.opsForValue().get(cacheKey);
    if (cachedSession != null) {
        return cachedSession;
    }
    
    // 3. 验证JWT Token
    if (!jwtTokenProvider.validateToken(token)) {
        return null;
    }
    
    // 4. 查询数据库
    MemberSession session = sessionMapper.selectMemberSessionByToken(token);
    if (session != null) {
        // 5. 缓存结果，使用与JWT相同的过期时间
        long ttlSeconds = jwtTokenProvider.getExpirationInSeconds();
        redisTemplate.opsForValue().set(cacheKey, session, ttlSeconds, TimeUnit.SECONDS);
    }
    
    return session;
}
```

#### getActiveSessionsForMember方法

```java
@Override
public List<MemberSession> getActiveSessionsForMember(Long memberId) {
    // 1. 构建缓存键
    String cacheKey = CacheConstants.BFF_MEMBER_SESSIONS_BY_ID_KEY + "::" + memberId;

    // 2. 尝试从缓存获取
    @SuppressWarnings("unchecked")
    List<MemberSession> cachedSessions = (List<MemberSession>) redisTemplate.opsForValue().get(cacheKey);
    if (cachedSessions != null) {
        return cachedSessions;
    }

    // 3. 缓存未命中，查询数据库
    List<MemberSession> sessions = sessionMapper.selectMemberSessionsByMemberId(memberId);

    // 4. 缓存结果，使用与JWT相同的过期时间
    long ttlSeconds = jwtTokenProvider.getExpirationInSeconds();
    redisTemplate.opsForValue().set(cacheKey, sessions, ttlSeconds, TimeUnit.SECONDS);

    return sessions;
}
```

### 3. 更新缓存清除逻辑
更新了多个地方的缓存清除逻辑，使用新的缓存键格式：

#### terminateSession方法
```java
// 手动清除 token 对应的缓存
String cacheKey = CacheConstants.BFF_MEMBER_SESSION_BY_TOKEN_KEY + "::" + session.getToken();
redisTemplate.delete(cacheKey);
```

#### refreshSessionCache方法
```java
@Override
public void refreshSessionCache(Long memberId) {
    // 1. 从数据库查询最新的活跃会话
    List<MemberSession> latestSessions = sessionMapper.selectMemberSessionsByMemberId(memberId);

    // 2. 手动更新缓存，使用与JWT相同的过期时间
    String cacheKey = CacheConstants.BFF_MEMBER_SESSIONS_BY_ID_KEY + "::" + memberId;
    long ttlSeconds = jwtTokenProvider.getExpirationInSeconds();
    redisTemplate.opsForValue().set(cacheKey, latestSessions, ttlSeconds, TimeUnit.SECONDS);
}
```

#### MemberAuthServiceImpl中的缓存清除
```java
// 清除会话列表缓存（使用新的缓存键格式）
String sessionCacheKey = CacheConstants.BFF_MEMBER_SESSIONS_BY_ID_KEY + "::" + memberId;
redisTemplate.delete(sessionCacheKey);
```

## 改进效果

### 1. 一致性保证
- Session缓存的TTL现在动态地从JWT配置中获取
- 确保两者始终保持一致，无需手动同步

### 2. 配置集中化
- 只需要在一个地方（`jwt.expiration`）配置过期时间
- 减少了配置错误的可能性

### 3. 维护性提升
- 修改JWT过期时间时，session缓存会自动使用相同的时间
- 降低了维护成本

## 测试验证

创建了 `MemberSessionServiceImplTest` 测试类，包含以下测试用例：

#### validateSession方法测试
1. **缓存命中测试**: 验证缓存命中时不会调用数据库和JWT验证
2. **缓存未命中测试**: 验证缓存未命中时会设置正确的TTL
3. **JWT验证失败测试**: 验证JWT无效时不会缓存结果

#### getActiveSessionsForMember方法测试
4. **会话列表缓存命中测试**: 验证缓存命中时不会调用数据库查询
5. **会话列表缓存未命中测试**: 验证缓存未命中时会设置正确的TTL

#### 其他测试
6. **TTL一致性测试**: 验证获取的JWT过期时间配置正确
7. **缓存清除测试**: 验证终止session时正确清除缓存
8. **缓存刷新测试**: 验证refreshSessionCache方法正确更新缓存

## 问题修复记录

### ClassCastException问题
**问题描述**: 在`DuplicateLoginServiceImpl.kickOldestSessionsAndReturn`方法中出现类型转换异常：
```
java.lang.ClassCastException: class com.alibaba.fastjson2.JSONObject cannot be cast to class ai.showlab.bff.entity.domain.v1.member.MemberSession
```

**根本原因**:
- 修改为手动缓存管理后，直接使用`RedisTemplate`获取数据
- Redis反序列化时将数据转换为`JSONObject`而不是`MemberSession`对象
- 业务代码直接强制类型转换导致异常

**解决方案**:
1. **添加类型转换方法**:
   - `convertCachedSession`: 处理单个Session对象的转换
   - `convertCachedSessionList`: 处理Session列表的转换

2. **修改缓存获取逻辑**:
   ```java
   // 修改前
   MemberSession cachedSession = (MemberSession) redisTemplate.opsForValue().get(cacheKey);

   // 修改后
   Object cachedValue = redisTemplate.opsForValue().get(cacheKey);
   MemberSession cachedSession = convertCachedSession(cachedValue);
   ```

3. **增强错误处理**: 转换失败时返回null，触发数据库查询

**验证**: 创建了`CacheTypeConversionTest`测试类验证类型转换逻辑的正确性

## 注意事项

### 1. 缓存键格式变化
原来使用Spring Cache的键格式，现在使用自定义格式：
- 原格式: 由Spring Cache管理
- 新格式: `{CacheConstants.BFF_MEMBER_SESSION_BY_TOKEN_KEY}::{token}`

### 2. Redis反序列化类型转换问题
由于直接使用`RedisTemplate`，需要处理Redis反序列化时的类型转换：
- **问题**: Redis中的数据可能被反序列化为`JSONObject`而不是`MemberSession`
- **解决**: 添加了`convertCachedSession`和`convertCachedSessionList`方法处理类型转换
- **影响**: 确保缓存数据能正确转换为业务对象，避免`ClassCastException`

### 3. 性能影响
- 手动缓存管理相比注解方式略有性能开销，但可以忽略
- 获得了更好的控制能力和一致性保证
- 类型转换有轻微性能开销，但比重新查询数据库快得多

### 4. 兼容性
- 现有的缓存数据会在过期后自动清理
- 新的缓存逻辑向前兼容
- 类型转换逻辑能处理多种数据格式

## 建议

1. **监控缓存命中率**: 确保缓存策略有效
2. **定期检查配置**: 确保JWT过期时间设置合理
3. **考虑缓存预热**: 对于高频访问的session可以考虑预热策略
4. **日志监控**: 关注缓存相关的日志，及时发现问题

## 总结

通过这次改进，我们解决了多个session相关缓存时间与JWT过期时间不一致的问题：

### 修复的问题
1. ✅ **validateSession缓存**: 从注解方式改为手动管理，动态使用JWT过期时间
2. ✅ **getActiveSessionsForMember缓存**: 从30秒TTL改为与JWT一致的动态TTL
3. ✅ **缓存键格式统一**: 所有相关缓存使用一致的键格式
4. ✅ **缓存清除逻辑**: 更新了所有相关的缓存清除代码

### 性能提升
- **减少数据库查询**: 会话列表缓存从30秒延长到1小时，大幅减少数据库访问
- **提高缓存命中率**: 更长的缓存时间提高了缓存的有效性
- **避免缓存雪崩**: 统一的过期时间避免了缓存在不同时间点失效

### 一致性保证
- **配置集中化**: 只需在一个地方配置JWT过期时间
- **自动同步**: 所有session相关缓存自动使用相同的过期时间
- **降低维护成本**: 减少了配置不一致的风险

这是一个很好的实践，展示了如何在微服务架构中保持配置的一致性和提高系统性能。
