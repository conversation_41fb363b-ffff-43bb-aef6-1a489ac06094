package ai.showlab.bff.common.security.token;

import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.common.core.constant.CodeConstants;
import io.jsonwebtoken.*;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Base64;
import java.util.Date;
import java.util.Map;

/**
 * JWT Token 提供者
 * 用于生成、解析和验证 JWT Token。
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtTokenProvider {

    /**
     * JWT 密钥，从配置文件中读取
     */
    @Value("${jwt.secret:defaultSecretKeyForDevelopmentAndTestingWhichIsAtLeast32BytesLong}")
    private String secret;

    /**
     * JWT 有效期，单位毫秒，从配置文件中读取
     */
    @Value("${jwt.expiration:3600000}") // 默认1小时
    private long expiration;

    /**
     * 编码后的密钥
     */
    private String encodedSecret;

    @PostConstruct
    protected void init() {
        // 对密钥进行Base64编码，以确保其安全性和可用性
        this.encodedSecret = Base64.getEncoder().encodeToString(secret.getBytes());
    }

    /**
     * 生成 JWT Token
     *
     * @param subject Token 的主题 (通常是用户名或用户ID)
     * @param claims  附加的声明信息，例如用户ID、角色等
     * @return 生成的 JWT Token 字符串
     */
    public String generateToken(String subject, Map<String, Object> claims) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(SignatureAlgorithm.HS512, encodedSecret)
                .compact();
    }

    /**
     * 从 JWT Token 中获取所有声明 (Claims)
     *
     * @param token JWT Token 字符串
     * @return Claims 对象
     */
    public Claims getClaimsFromToken(String token) {
        try {
            return Jwts.parser().setSigningKey(encodedSecret).parseClaimsJws(token).getBody();
        } catch (SignatureException ex) {
            log.warn("JWT签名无效: {}", ex.getMessage());
            throw new BusinessException(CodeConstants.UNAUTHORIZED, "登录信息不正确，请重新登录");
        } catch (MalformedJwtException ex) {
            log.warn("JWT格式无效: {}", ex.getMessage());
            throw new BusinessException(CodeConstants.UNAUTHORIZED, "登录信息无效，请重新登录");
        } catch (ExpiredJwtException ex) {
            log.info("JWT token已过期: {}", ex.getMessage());
            throw new BusinessException(CodeConstants.UNAUTHORIZED, "登录状态已过期，请重新登录");
        } catch (UnsupportedJwtException ex) {
            log.warn("不支持的JWT格式: {}", ex.getMessage());
            throw new BusinessException(CodeConstants.UNAUTHORIZED, "登录信息异常，请重新登录");
        } catch (IllegalArgumentException ex) {
            log.warn("JWT参数无效: {}", ex.getMessage());
            throw new BusinessException(CodeConstants.UNAUTHORIZED, "登录信息有误，请重新登录");
        }
    }

    /**
     * 验证 JWT Token 是否有效
     *
     * @param token JWT Token 字符串
     * @return 如果 Token 有效则返回 true，否则返回 false (会抛出 BusinessException)
     */
    public boolean validateToken(String token) {
        if (!StringUtils.hasText(token)) {
            return false;
        }
        // 尝试解析，如果抛出异常则表示无效
        getClaimsFromToken(token);
        return true;
    }

    /**
     * 从 Token 中获取用户ID
     *
     * @param token JWT Token 字符串
     * @return 用户ID (Long 类型)
     */
    public Long getUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return Long.parseLong(claims.getSubject());
    }

    /**
     * 获取 JWT Token 的过期时间
     *
     * @param token JWT Token 字符串
     * @return 过期时间 (Date 对象)
     */
    public Date getExpirationDateFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getExpiration();
    }

    /**
     * 获取JWT过期时间配置（以秒为单位）
     * 用于与缓存TTL保持一致
     *
     * @return JWT过期时间（秒）
     */
    public long getExpirationInSeconds() {
        return expiration / 1000;
    }
}