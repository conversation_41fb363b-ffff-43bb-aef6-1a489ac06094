package ai.showlab.bff.service.v1.member.impl;

import ai.showlab.bff.common.security.token.JwtTokenProvider;
import ai.showlab.bff.entity.domain.v1.member.MemberSession;
import ai.showlab.bff.mapper.v1.member.MemberSessionMapper;
import ai.showlab.common.core.constant.CacheConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MemberSessionServiceImpl 测试类
 * 重点测试session缓存时间与JWT过期时间的一致性
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class MemberSessionServiceImplTest {

    @Mock
    private MemberSessionMapper sessionMapper;

    @Mock
    private CacheManager cacheManager;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private ValueOperations<String, Object> valueOperations;

    @Mock
    private JwtTokenProvider jwtTokenProvider;

    @InjectMocks
    private MemberSessionServiceImpl memberSessionService;

    private String testToken;
    private MemberSession testSession;

    @BeforeEach
    void setUp() {
        testToken = "test.jwt.token";
        testSession = new MemberSession();
        testSession.setId(1L);
        testSession.setMemberId(100L);
        testSession.setToken(testToken);
        testSession.setIsActive(true);

        // Mock RedisTemplate operations
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void testValidateSession_CacheHit() {
        // Given
        String cacheKey = CacheConstants.BFF_MEMBER_SESSION_BY_TOKEN_KEY + "::" + testToken;
        when(valueOperations.get(cacheKey)).thenReturn(testSession);

        // When
        MemberSession result = memberSessionService.validateSession(testToken);

        // Then
        assertNotNull(result);
        assertEquals(testSession.getId(), result.getId());
        assertEquals(testSession.getMemberId(), result.getMemberId());
        
        // 验证没有调用数据库查询
        verify(sessionMapper, never()).selectMemberSessionByToken(anyString());
        // 验证没有调用JWT验证
        verify(jwtTokenProvider, never()).validateToken(anyString());
    }

    @Test
    void testValidateSession_CacheMiss_ValidJWT() {
        // Given
        String cacheKey = CacheConstants.BFF_MEMBER_SESSION_BY_TOKEN_KEY + "::" + testToken;
        long jwtExpirationSeconds = 3600L; // 1小时
        
        when(valueOperations.get(cacheKey)).thenReturn(null); // 缓存未命中
        when(jwtTokenProvider.validateToken(testToken)).thenReturn(true);
        when(jwtTokenProvider.getExpirationInSeconds()).thenReturn(jwtExpirationSeconds);
        when(sessionMapper.selectMemberSessionByToken(testToken)).thenReturn(testSession);

        // When
        MemberSession result = memberSessionService.validateSession(testToken);

        // Then
        assertNotNull(result);
        assertEquals(testSession.getId(), result.getId());
        
        // 验证JWT验证被调用
        verify(jwtTokenProvider).validateToken(testToken);
        // 验证数据库查询被调用
        verify(sessionMapper).selectMemberSessionByToken(testToken);
        // 验证缓存被设置，且TTL与JWT过期时间一致
        verify(valueOperations).set(cacheKey, testSession, jwtExpirationSeconds, TimeUnit.SECONDS);
        verify(jwtTokenProvider).getExpirationInSeconds();
    }

    @Test
    void testValidateSession_InvalidJWT() {
        // Given
        String cacheKey = CacheConstants.BFF_MEMBER_SESSION_BY_TOKEN_KEY + "::" + testToken;
        
        when(valueOperations.get(cacheKey)).thenReturn(null); // 缓存未命中
        when(jwtTokenProvider.validateToken(testToken)).thenReturn(false);

        // When
        MemberSession result = memberSessionService.validateSession(testToken);

        // Then
        assertNull(result);
        
        // 验证JWT验证被调用
        verify(jwtTokenProvider).validateToken(testToken);
        // 验证数据库查询没有被调用
        verify(sessionMapper, never()).selectMemberSessionByToken(anyString());
        // 验证缓存没有被设置
        verify(valueOperations, never()).set(anyString(), any(), anyLong(), any(TimeUnit.class));
    }

    @Test
    void testJwtExpirationConsistency() {
        // Given
        long expectedExpirationSeconds = 7200L; // 2小时
        when(jwtTokenProvider.getExpirationInSeconds()).thenReturn(expectedExpirationSeconds);

        // When
        long actualExpiration = jwtTokenProvider.getExpirationInSeconds();

        // Then
        assertEquals(expectedExpirationSeconds, actualExpiration);
        
        // 这个测试确保我们可以获取JWT的过期时间配置
        // 在实际使用中，这个值应该与配置文件中的jwt.expiration保持一致
    }

    @Test
    void testTerminateSession_CacheClearance() {
        // Given
        Long memberId = 100L;
        Long sessionId = 1L;
        String cacheKey = CacheConstants.BFF_MEMBER_SESSION_BY_TOKEN_KEY + "::" + testToken;
        
        when(sessionMapper.selectMemberSessionById(sessionId)).thenReturn(testSession);
        when(sessionMapper.deactivateMemberSession(testToken)).thenReturn(1);

        // When
        memberSessionService.terminateSession(memberId, sessionId);

        // Then
        // 验证缓存被清除
        verify(redisTemplate).delete(cacheKey);
        // 验证数据库更新被调用
        verify(sessionMapper).deactivateMemberSession(testToken);
    }
}
