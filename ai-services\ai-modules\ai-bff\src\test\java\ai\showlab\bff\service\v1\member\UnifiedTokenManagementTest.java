package ai.showlab.bff.service.v1.member;

import ai.showlab.bff.common.security.token.JwtTokenProvider;
import ai.showlab.bff.entity.domain.v1.member.MemberSession;
import ai.showlab.bff.service.v1.member.impl.MemberSessionServiceImpl;
import ai.showlab.common.core.utils.CommUtil;
import com.ruoyi.common.core.utils.ip.IpUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.CacheManager;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 统一Token管理功能测试
 * 
 * 测试JWT和Session的统一管理，验证：
 * 1. Session表直接存储JWT Token
 * 2. 过期时间一致性
 * 3. 验证逻辑统一
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class UnifiedTokenManagementTest {

    @Mock
    private ai.showlab.bff.mapper.v1.member.MemberSessionMapper sessionMapper;

    @Mock
    private CacheManager cacheManager;

    @Mock
    private JwtTokenProvider jwtTokenProvider;

    @Mock
    private HttpServletRequest request;

    @InjectMocks
    private MemberSessionServiceImpl memberSessionService;

    private Long testMemberId = 12345L;
    private String testJwtToken = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxMjM0NSIsIm1lbWJlcklkIjoxMjM0NSwidXNlcm5hbWUiOiJ0ZXN0dXNlciIsIm5pY2tuYW1lIjoi5rWL6K-V55So5oi3IiwiYXV0aFR5cGUiOjEsImlhdCI6MTczNTk4MDAwMCwiZXhwIjoxNzM1OTgzNjAwfQ.test";
    private Date testExpirationDate = new Date(System.currentTimeMillis() + 3600000); // 1小时后过期

    @BeforeEach
    void setUp() {
        // 模拟IP获取
        when(request.getRemoteAddr()).thenReturn("*************");
        
        // 模拟JWT Token Provider
        when(jwtTokenProvider.generateToken(anyString(), any(Map.class))).thenReturn(testJwtToken);
        when(jwtTokenProvider.getExpirationDateFromToken(testJwtToken)).thenReturn(testExpirationDate);
        when(jwtTokenProvider.validateToken(testJwtToken)).thenReturn(true);
    }

    @Test
    void testCreateSessionForMember_ShouldGenerateJwtAndCreateSession() {
        // Given
        MemberSession expectedSession = createTestSession();
        when(sessionMapper.insertMemberSession(any(MemberSession.class))).thenReturn(1);

        // When
        MemberSession result = memberSessionService.createSessionForMember(testMemberId, request);

        // Then
        assertNotNull(result);
        assertEquals(testMemberId, result.getMemberId());
        assertEquals(testJwtToken, result.getToken()); // 验证Session直接存储JWT Token
        assertTrue(result.getIsActive());
        
        // 验证JWT Token生成被调用
        verify(jwtTokenProvider).generateToken(eq(testMemberId.toString()), any(Map.class));
        verify(jwtTokenProvider).getExpirationDateFromToken(testJwtToken);
        verify(sessionMapper).insertMemberSession(any(MemberSession.class));
    }

    @Test
    void testCreateSessionWithJwtToken_ShouldUseProvidedJwtToken() {
        // Given
        MemberSession expectedSession = createTestSession();
        when(sessionMapper.insertMemberSession(any(MemberSession.class))).thenReturn(1);

        // When
        MemberSession result = memberSessionService.createSessionWithJwtToken(testMemberId, testJwtToken, request);

        // Then
        assertNotNull(result);
        assertEquals(testMemberId, result.getMemberId());
        assertEquals(testJwtToken, result.getToken()); // 验证直接使用提供的JWT Token
        assertTrue(result.getIsActive());
        
        // 验证过期时间一致性
        assertEquals(CommUtil.toOffsetDateTime(testExpirationDate), result.getExpireTime());
        
        verify(jwtTokenProvider).getExpirationDateFromToken(testJwtToken);
        verify(sessionMapper).insertMemberSession(any(MemberSession.class));
    }

    @Test
    void testValidateSession_ShouldValidateJwtFirst() {
        // Given
        MemberSession mockSession = createTestSession();
        when(jwtTokenProvider.validateToken(testJwtToken)).thenReturn(true);
        when(sessionMapper.selectMemberSessionByToken(testJwtToken)).thenReturn(mockSession);

        // When
        MemberSession result = memberSessionService.validateSession(testJwtToken);

        // Then
        assertNotNull(result);
        assertEquals(testMemberId, result.getMemberId());
        assertEquals(testJwtToken, result.getToken());
        
        // 验证JWT验证优先
        verify(jwtTokenProvider).validateToken(testJwtToken);
        verify(sessionMapper).selectMemberSessionByToken(testJwtToken);
    }

    @Test
    void testValidateSession_ShouldReturnNullWhenJwtInvalid() {
        // Given
        when(jwtTokenProvider.validateToken(testJwtToken)).thenReturn(false);

        // When
        MemberSession result = memberSessionService.validateSession(testJwtToken);

        // Then
        assertNull(result);
        
        // 验证JWT验证失败后不查询Session
        verify(jwtTokenProvider).validateToken(testJwtToken);
        verify(sessionMapper, never()).selectMemberSessionByToken(any());
    }

    @Test
    void testValidateSession_ShouldReturnNullWhenJwtThrowsException() {
        // Given
        when(jwtTokenProvider.validateToken(testJwtToken)).thenThrow(new RuntimeException("JWT expired"));

        // When
        MemberSession result = memberSessionService.validateSession(testJwtToken);

        // Then
        assertNull(result);
        
        // 验证异常处理
        verify(jwtTokenProvider).validateToken(testJwtToken);
        verify(sessionMapper, never()).selectMemberSessionByToken(any());
    }

    @Test
    void testTerminateSessionByToken_ShouldValidateJwtAndTerminateSession() {
        // Given
        MemberSession mockSession = createTestSession();
        mockSession.setId(1L);
        
        when(jwtTokenProvider.validateToken(testJwtToken)).thenReturn(true);
        when(sessionMapper.selectMemberSessionByToken(testJwtToken)).thenReturn(mockSession);
        when(sessionMapper.selectMemberSessionById(1L)).thenReturn(mockSession);
        when(sessionMapper.deactivateMemberSession(testJwtToken)).thenReturn(1);

        // When
        memberSessionService.terminateSessionByToken(testMemberId, testJwtToken);

        // Then
        verify(jwtTokenProvider).validateToken(testJwtToken);
        verify(sessionMapper).selectMemberSessionByToken(testJwtToken);
        verify(sessionMapper).deactivateMemberSession(testJwtToken);
    }

    @Test
    void testTerminateSessionByToken_ShouldNotTerminateWhenJwtInvalid() {
        // Given
        when(jwtTokenProvider.validateToken(testJwtToken)).thenReturn(false);

        // When
        memberSessionService.terminateSessionByToken(testMemberId, testJwtToken);

        // Then
        verify(jwtTokenProvider).validateToken(testJwtToken);
        verify(sessionMapper, never()).selectMemberSessionByToken(any());
        verify(sessionMapper, never()).deactivateMemberSession(any());
    }

    @Test
    void testExpirationTimeConsistency() {
        // Given
        Date jwtExpiration = new Date(System.currentTimeMillis() + 7200000); // 2小时后
        when(jwtTokenProvider.getExpirationDateFromToken(testJwtToken)).thenReturn(jwtExpiration);
        when(sessionMapper.insertMemberSession(any(MemberSession.class))).thenReturn(1);

        // When
        MemberSession result = memberSessionService.createSessionWithJwtToken(testMemberId, testJwtToken, request);

        // Then
        assertNotNull(result);
        assertEquals(CommUtil.toOffsetDateTime(jwtExpiration), result.getExpireTime());
        
        // 验证Session过期时间与JWT一致
        verify(jwtTokenProvider).getExpirationDateFromToken(testJwtToken);
    }

    private MemberSession createTestSession() {
        MemberSession session = new MemberSession();
        session.setMemberId(testMemberId);
        session.setToken(testJwtToken);
        session.setDeviceType(1);
        session.setDeviceInfo("Test Device");
        session.setIpAddress("*************");
        session.setLoginTime(CommUtil.toOffsetDateTime(null));
        session.setExpireTime(CommUtil.toOffsetDateTime(testExpirationDate));
        session.setIsActive(true);
        return session;
    }
}
